//@version=5
strategy("Professional Trading System - EMA RSI Multi-Confirmation",
         shorttitle="PTS-EMA-RSI",
         overlay=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=2,
         commission_type=strategy.commission.percent,
         commission_value=0.1)

// ═══════════════════════════════════════════════════════════════════════════════════════
// INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════

// EMA Settings
ema_fast_length = input.int(12, "Fast EMA Length", minval=1, group="EMA Settings")
ema_slow_length = input.int(26, "Slow EMA Length", minval=1, group="EMA Settings")
ema_trend_length = input.int(50, "Trend EMA Length", minval=1, group="EMA Settings")

// RSI Settings
rsi_length = input.int(14, "RSI Length", minval=1, group="RSI Settings")
rsi_overbought = input.float(70, "RSI Overbought", minval=50, maxval=100, group="RSI Settings")
rsi_oversold = input.float(30, "RSI Oversold", minval=0, maxval=50, group="RSI Settings")

// Additional Confirmation Indicators
use_macd = input.bool(true, "Use MACD Confirmation", group="Confirmation Indicators")
use_volume = input.bool(true, "Use Volume Confirmation", group="Confirmation Indicators")
use_bollinger = input.bool(true, "Use Bollinger Bands", group="Confirmation Indicators")

// Risk Management
tp1_percent = input.float(1.5, "Take Profit 1 (%)", minval=0.1, maxval=10, step=0.1, group="Risk Management")
tp2_percent = input.float(3.0, "Take Profit 2 (%)", minval=0.1, maxval=20, step=0.1, group="Risk Management")
sl_percent = input.float(1.0, "Stop Loss (%)", minval=0.1, maxval=10, step=0.1, group="Risk Management")

// Panel Settings
show_panel = input.bool(true, "Show Information Panel", group="Display Settings")
panel_position = input.string("top_right", "Panel Position",
                              options=["top_left", "top_right", "bottom_left", "bottom_right"],
                              group="Display Settings")

// ═══════════════════════════════════════════════════════════════════════════════════════
// TECHNICAL INDICATORS
// ═══════════════════════════════════════════════════════════════════════════════════════

// EMA Calculations
ema_fast = ta.ema(close, ema_fast_length)
ema_slow = ta.ema(close, ema_slow_length)
ema_trend = ta.ema(close, ema_trend_length)

// RSI Calculation
rsi = ta.rsi(close, rsi_length)

// MACD Calculation
[macd_line, signal_line, macd_histogram] = ta.macd(close, 12, 26, 9)

// Volume Analysis
volume_sma = ta.sma(volume, 20)
volume_spike = volume > volume_sma * 1.5

// Bollinger Bands
bb_length = 20
bb_mult = 2.0
bb_basis = ta.sma(close, bb_length)
bb_dev = bb_mult * ta.stdev(close, bb_length)
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev

// ═══════════════════════════════════════════════════════════════════════════════════════
// SIGNAL LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════

// Primary EMA Signal
ema_bullish = ema_fast > ema_slow and close > ema_trend
ema_bearish = ema_fast < ema_slow and close < ema_trend

// RSI Conditions
rsi_bullish = rsi > 50 and rsi < rsi_overbought
rsi_bearish = rsi < 50 and rsi > rsi_oversold
rsi_neutral = rsi >= rsi_overbought or rsi <= rsi_oversold

// MACD Confirmation
macd_bullish = use_macd ? (macd_line > signal_line and macd_histogram > 0) : true
macd_bearish = use_macd ? (macd_line < signal_line and macd_histogram < 0) : true

// Volume Confirmation
volume_confirm = use_volume ? volume_spike : true

// Bollinger Bands Confirmation
bb_bullish = use_bollinger ? (close > bb_basis and close < bb_upper) : true
bb_bearish = use_bollinger ? (close < bb_basis and close > bb_lower) : true

// Combined Signal Logic
strong_bullish = ema_bullish and rsi_bullish and macd_bullish and volume_confirm and bb_bullish
strong_bearish = ema_bearish and rsi_bearish and macd_bearish and volume_confirm and bb_bearish

// Neutral/Wait Conditions
wait_breakout = (close > bb_lower and close < bb_upper and math.abs(ema_fast - ema_slow) < close * 0.001) or rsi_neutral
neutral_signal = wait_breakout or (not strong_bullish and not strong_bearish)

// ═══════════════════════════════════════════════════════════════════════════════════════
// SIGNAL CLASSIFICATION
// ═══════════════════════════════════════════════════════════════════════════════════════

var string signal_type = "NEUTRAL"
var string signal_reason = "Waiting for clear signal"
var color signal_color = color.gray

if strong_bullish
    signal_type := "LONG"
    signal_reason := "EMA Cross + RSI Bullish + Confirmations"
    signal_color := color.green
else if strong_bearish
    signal_type := "SHORT"
    signal_reason := "EMA Cross + RSI Bearish + Confirmations"
    signal_color := color.red
else if wait_breakout
    signal_type := "NEUTRAL"
    signal_reason := "Waiting for breakout/RSI reset"
    signal_color := color.orange
else
    signal_type := "NEUTRAL"
    signal_reason := "Insufficient confirmation signals"
    signal_color := color.gray

// ═══════════════════════════════════════════════════════════════════════════════════════
// TRADING LOGIC & POSITION MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════════════

// Entry Conditions
long_entry = strong_bullish and strategy.position_size == 0
short_entry = strong_bearish and strategy.position_size == 0

// Calculate Position Levels
var float entry_price = na
var float tp1_price = na
var float tp2_price = na
var float sl_price = na

if long_entry
    entry_price := close
    tp1_price := entry_price * (1 + tp1_percent / 100)
    tp2_price := entry_price * (1 + tp2_percent / 100)
    sl_price := entry_price * (1 - sl_percent / 100)
    strategy.entry("Long", strategy.long)

if short_entry
    entry_price := close
    tp1_price := entry_price * (1 - tp1_percent / 100)
    tp2_price := entry_price * (1 - tp2_percent / 100)
    sl_price := entry_price * (1 + sl_percent / 100)
    strategy.entry("Short", strategy.short)

// Exit Logic
if strategy.position_size > 0  // Long position
    strategy.exit("TP1", "Long", limit=tp1_price, stop=sl_price, qty_percent=50)
    strategy.exit("TP2", "Long", limit=tp2_price, stop=sl_price, qty_percent=50)

if strategy.position_size < 0  // Short position
    strategy.exit("TP1", "Short", limit=tp1_price, stop=sl_price, qty_percent=50)
    strategy.exit("TP2", "Short", limit=tp2_price, stop=sl_price, qty_percent=50)

// ═══════════════════════════════════════════════════════════════════════════════════════
// VISUAL ELEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot EMAs
plot(ema_fast, "Fast EMA", color=color.blue, linewidth=2)
plot(ema_slow, "Slow EMA", color=color.red, linewidth=2)
plot(ema_trend, "Trend EMA", color=color.yellow, linewidth=3)

// Plot Bollinger Bands
bb_upper_plot = plot(bb_upper, "BB Upper", color=color.gray, linewidth=1)
bb_lower_plot = plot(bb_lower, "BB Lower", color=color.gray, linewidth=1)
fill(bb_upper_plot, bb_lower_plot, color=color.new(color.gray, 95), title="BB Fill")

// Plot Entry Signals
plotshape(long_entry, "Long Signal", shape.triangleup, location.belowbar, color.green, size=size.normal)
plotshape(short_entry, "Short Signal", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Plot Position Levels
plot(strategy.position_size != 0 ? entry_price : na, "Entry Price", color=color.white, linewidth=2, style=plot.style_linebr)
plot(strategy.position_size != 0 ? tp1_price : na, "TP1", color=color.green, linewidth=1, style=plot.style_linebr)
plot(strategy.position_size != 0 ? tp2_price : na, "TP2", color=color.lime, linewidth=1, style=plot.style_linebr)
plot(strategy.position_size != 0 ? sl_price : na, "Stop Loss", color=color.red, linewidth=1, style=plot.style_linebr)

// ═══════════════════════════════════════════════════════════════════════════════════════
// INFORMATION PANEL
// ═══════════════════════════════════════════════════════════════════════════════════════

if show_panel
    var table info_table = table.new(
         position = panel_position == "top_left" ? position.top_left :
                   panel_position == "top_right" ? position.top_right :
                   panel_position == "bottom_left" ? position.bottom_left : position.bottom_right,
         columns = 2,
         rows = 12,
         bgcolor = color.new(color.black, 85),
         border_width = 2,
         border_color = color.white)

    // Header
    table.cell(info_table, 0, 0, "Professional Trading System", text_color=color.white, text_size=size.normal, bgcolor=color.new(color.blue, 70))
    table.cell(info_table, 1, 0, "Status", text_color=color.white, text_size=size.normal, bgcolor=color.new(color.blue, 70))

    // Current Signal
    table.cell(info_table, 0, 1, "Signal Type:", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 1, signal_type, text_color=signal_color, text_size=size.small, bgcolor=color.new(signal_color, 90))

    // Signal Reason
    table.cell(info_table, 0, 2, "Reason:", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 2, signal_reason, text_color=color.white, text_size=size.tiny)

    // Technical Indicators Status
    table.cell(info_table, 0, 3, "EMA Trend:", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 3, ema_bullish ? "Bullish" : ema_bearish ? "Bearish" : "Neutral",
               text_color=ema_bullish ? color.green : ema_bearish ? color.red : color.gray, text_size=size.small)

    table.cell(info_table, 0, 4, "RSI (" + str.tostring(rsi_length) + "):", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 4, str.tostring(math.round(rsi, 1)),
               text_color=rsi > 70 ? color.red : rsi < 30 ? color.green : color.white, text_size=size.small)

    table.cell(info_table, 0, 5, "MACD:", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 5, macd_bullish ? "Bullish" : macd_bearish ? "Bearish" : "Neutral",
               text_color=macd_bullish ? color.green : macd_bearish ? color.red : color.gray, text_size=size.small)

    // Volume Analysis
    table.cell(info_table, 0, 6, "Volume:", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 6, volume_spike ? "High" : "Normal",
               text_color=volume_spike ? color.yellow : color.white, text_size=size.small)

    // Position Information
    table.cell(info_table, 0, 7, "Position:", text_color=color.white, text_size=size.small)
    position_text = strategy.position_size > 0 ? "LONG" : strategy.position_size < 0 ? "SHORT" : "NONE"
    position_color = strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.gray
    table.cell(info_table, 1, 7, position_text, text_color=position_color, text_size=size.small)

    // Entry Price
    if strategy.position_size != 0
        table.cell(info_table, 0, 8, "Entry Price:", text_color=color.white, text_size=size.small)
        table.cell(info_table, 1, 8, str.tostring(entry_price, "#.####"), text_color=color.white, text_size=size.small)

        // Take Profit Levels
        table.cell(info_table, 0, 9, "TP1 (" + str.tostring(tp1_percent) + "%):", text_color=color.white, text_size=size.small)
        table.cell(info_table, 1, 9, str.tostring(tp1_price, "#.####"), text_color=color.green, text_size=size.small)

        table.cell(info_table, 0, 10, "TP2 (" + str.tostring(tp2_percent) + "%):", text_color=color.white, text_size=size.small)
        table.cell(info_table, 1, 10, str.tostring(tp2_price, "#.####"), text_color=color.lime, text_size=size.small)

        // Stop Loss
        table.cell(info_table, 0, 11, "SL (" + str.tostring(sl_percent) + "%):", text_color=color.white, text_size=size.small)
        table.cell(info_table, 1, 11, str.tostring(sl_price, "#.####"), text_color=color.red, text_size=size.small)
    else
        // Strategy Information when no position
        table.cell(info_table, 0, 8, "Strategy:", text_color=color.white, text_size=size.small)
        table.cell(info_table, 1, 8, "EMA + RSI + Multi-Confirm", text_color=color.white, text_size=size.tiny)

        table.cell(info_table, 0, 9, "Timeframe:", text_color=color.white, text_size=size.small)
        table.cell(info_table, 1, 9, timeframe.period, text_color=color.white, text_size=size.small)

        table.cell(info_table, 0, 10, "Next Action:", text_color=color.white, text_size=size.small)
        next_action = wait_breakout ? "Wait for breakout" :
                     rsi_neutral ? "Wait RSI reset" :
                     "Monitor signals"
        table.cell(info_table, 1, 10, next_action, text_color=color.orange, text_size=size.tiny)

        table.cell(info_table, 0, 11, "Risk/Reward:", text_color=color.white, text_size=size.small)
        rr_ratio = tp2_percent / sl_percent
        table.cell(info_table, 1, 11, "1:" + str.tostring(rr_ratio, "#.#"), text_color=color.yellow, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════
// ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Alert Conditions
alertcondition(long_entry, title="Long Entry Signal",
               message="LONG ENTRY: EMA Cross + RSI Bullish + Confirmations. Entry: {{close}}, TP1: {{plot('TP1')}}, TP2: {{plot('TP2')}}, SL: {{plot('Stop Loss')}}")

alertcondition(short_entry, title="Short Entry Signal",
               message="SHORT ENTRY: EMA Cross + RSI Bearish + Confirmations. Entry: {{close}}, TP1: {{plot('TP1')}}, TP2: {{plot('TP2')}}, SL: {{plot('Stop Loss')}}")

alertcondition(wait_breakout, title="Neutral - Wait for Breakout",
               message="NEUTRAL SIGNAL: Waiting for breakout or RSI reset. Current RSI: " + str.tostring(rsi, "#.#"))

// Background color based on signal strength
bgcolor(strong_bullish ? color.new(color.green, 95) :
        strong_bearish ? color.new(color.red, 95) :
        wait_breakout ? color.new(color.orange, 98) : na, title="Signal Background")

// ═══════════════════════════════════════════════════════════════════════════════════════
// STRATEGY SUMMARY
// ═══════════════════════════════════════════════════════════════════════════════════════
// This Professional Trading System combines:
// 1. EMA Cross Strategy (Fast vs Slow EMA with Trend Filter)
// 2. RSI Momentum Confirmation (avoiding overbought/oversold extremes)
// 3. MACD Trend Confirmation (optional)
// 4. Volume Spike Confirmation (optional)
// 5. Bollinger Bands Position Filter (optional)
// 6. Professional Risk Management (TP1: 50% at 1.5%, TP2: 50% at 3%, SL: 1%)
// 7. Neutral signals for unclear market conditions
// 8. Real-time information panel with all relevant data
// ═══════════════════════════════════════════════════════════════════════════════════════