//@version=6
strategy("🤖 AI Professional Trading Assistant",
         shorttitle="AI-PTA",
         overlay=true,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=2,
         commission_type=strategy.commission.percent,
         commission_value=0.075,
         pyramiding=0,
         calc_on_every_tick=false)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎛️ AI CONFIGURATION & INTELLIGENCE SETTINGS
// ═══════════════════════════════════════════════════════════════════════════════════════

// AI Decision Making Parameters
ai_sensitivity = input.float(0.75, "🧠 AI Decision Sensitivity", minval=0.1, maxval=1.0, step=0.05, tooltip="Higher = More aggressive, Lower = More conservative", group="🤖 AI Intelligence")
market_regime_detection = input.bool(true, "📊 Market Regime Detection", tooltip="AI analyzes trending vs ranging markets", group="🤖 AI Intelligence")
adaptive_timeframe = input.bool(true, "⏰ Adaptive Timeframe Analysis", tooltip="AI considers multiple timeframes", group="🤖 AI Intelligence")
volatility_adjustment = input.bool(true, "📈 Volatility-Based Adjustments", tooltip="AI adjusts strategy based on market volatility", group="🤖 AI Intelligence")

// Core Technical Settings (AI Optimized)
ema_fast = input.int(9, "⚡ Fast EMA", minval=5, maxval=21, group="📈 Technical Core")
ema_slow = input.int(21, "🐌 Slow EMA", minval=15, maxval=50, group="📈 Technical Core")
ema_trend = input.int(55, "🌊 Trend EMA", minval=30, maxval=100, group="📈 Technical Core")
rsi_period = input.int(14, "🎯 RSI Period", minval=7, maxval=21, group="📈 Technical Core")

// AI Risk Management (Dynamic)
base_tp1 = input.float(1.2, "🎯 Base TP1 (%)", minval=0.5, maxval=3.0, step=0.1, group="💰 AI Risk Management")
base_tp2 = input.float(2.8, "🚀 Base TP2 (%)", minval=1.0, maxval=8.0, step=0.1, group="💰 AI Risk Management")
base_sl = input.float(0.8, "🛡️ Base Stop Loss (%)", minval=0.3, maxval=2.0, step=0.1, group="💰 AI Risk Management")
dynamic_sizing = input.bool(true, "📊 Dynamic Position Sizing", tooltip="AI adjusts position size based on confidence", group="💰 AI Risk Management")

// Visual & Interface
show_ai_panel = input.bool(true, "🖥️ Show AI Dashboard", group="🎨 Interface")
panel_style = input.string("Modern", "🎨 Panel Style", options=["Modern", "Classic", "Minimal"], group="🎨 Interface")
show_signals = input.bool(true, "📍 Show Entry Signals", group="🎨 Interface")
show_levels = input.bool(true, "📏 Show Trade Levels", group="🎨 Interface")

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧠 AI TECHNICAL ANALYSIS ENGINE
// ═══════════════════════════════════════════════════════════════════════════════════════

// Core Moving Averages
ema_fast_line = ta.ema(close, ema_fast)
ema_slow_line = ta.ema(close, ema_slow)
ema_trend_line = ta.ema(close, ema_trend)

// Advanced RSI Analysis
rsi = ta.rsi(close, rsi_period)
rsi_ma = ta.sma(rsi, 5)  // Smoothed RSI for better signals
rsi_momentum = rsi - rsi[1]

// Multi-Timeframe MACD
[macd_line, signal_line, macd_histogram] = ta.macd(close, 12, 26, 9)
macd_momentum = macd_histogram - macd_histogram[1]

// Advanced Volume Analysis
volume_ma = ta.sma(volume, 20)
volume_ratio = volume / volume_ma
volume_trend = ta.sma(volume_ratio, 5)

// Volatility Indicators
atr = ta.atr(14)
atr_ma = ta.sma(atr, 20)
volatility_ratio = atr / atr_ma

// Bollinger Bands with Dynamic Periods
bb_period = volatility_adjustment ? math.round(20 * (2 - volatility_ratio)) : 20
bb_mult = volatility_adjustment ? (1.8 + volatility_ratio * 0.4) : 2.0
bb_basis = ta.sma(close, bb_period)
bb_dev = bb_mult * ta.stdev(close, bb_period)
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
bb_squeeze = (bb_upper - bb_lower) / bb_basis < 0.02

// Market Structure Analysis
higher_high = high > ta.highest(high[1], 10)
lower_low = low < ta.lowest(low[1], 10)
market_structure = higher_high ? 1 : lower_low ? -1 : 0

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🤖 AI MARKET REGIME DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Trend Strength Calculation
trend_strength = math.abs(ema_fast_line - ema_slow_line) / close
trend_direction = ema_fast_line > ema_slow_line ? 1 : -1
trend_consistency = ta.sma(trend_direction, 10)

// Market Regime Classification
is_trending = trend_strength > 0.005 and math.abs(trend_consistency) > 0.6
is_ranging = not is_trending and bb_squeeze
is_volatile = volatility_ratio > 1.3
is_consolidating = not is_trending and not is_volatile and rsi > 40 and rsi < 60

// AI Confidence Scoring (0-100)
confidence_score = 0.0
confidence_score := confidence_score + (is_trending ? 25 : 0)
confidence_score := confidence_score + (math.abs(rsi_momentum) > 2 ? 20 : 0)
confidence_score := confidence_score + (volume_ratio > 1.2 ? 15 : 0)
confidence_score := confidence_score + (math.abs(macd_momentum) > 0 ? 15 : 0)
confidence_score := confidence_score + (market_structure != 0 ? 25 : 0)

// Adaptive Confidence Threshold
confidence_threshold = ai_sensitivity * 70

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎯 AI SIGNAL GENERATION ENGINE
// ═══════════════════════════════════════════════════════════════════════════════════════

// Primary Trend Analysis
ema_alignment_bull = ema_fast_line > ema_slow_line and ema_slow_line > ema_trend_line and close > ema_fast_line
ema_alignment_bear = ema_fast_line < ema_slow_line and ema_slow_line < ema_trend_line and close < ema_fast_line

// Advanced RSI Analysis
rsi_bullish_zone = rsi > 45 and rsi < 75 and rsi_momentum > 0
rsi_bearish_zone = rsi < 55 and rsi > 25 and rsi_momentum < 0
rsi_oversold_recovery = rsi < 35 and rsi > rsi[1] and rsi[1] > rsi[2]
rsi_overbought_decline = rsi > 65 and rsi < rsi[1] and rsi[1] < rsi[2]

// MACD Intelligence
macd_bullish_cross = ta.crossover(macd_line, signal_line) and macd_histogram > macd_histogram[1]
macd_bearish_cross = ta.crossunder(macd_line, signal_line) and macd_histogram < macd_histogram[1]
macd_momentum_bull = macd_line > signal_line and macd_momentum > 0
macd_momentum_bear = macd_line < signal_line and macd_momentum < 0

// Volume Intelligence
volume_confirmation_bull = volume_ratio > 1.1 and volume_trend > 1.0
volume_confirmation_bear = volume_ratio > 1.1 and volume_trend > 1.0
volume_accumulation = volume_ratio > 1.3 and close > open
volume_distribution = volume_ratio > 1.3 and close < open

// Market Structure Confirmation
structure_bullish = market_structure >= 0 and close > bb_basis
structure_bearish = market_structure <= 0 and close < bb_basis

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🧠 AI DECISION MATRIX
// ═══════════════════════════════════════════════════════════════════════════════════════

// Bullish Signal Strength (0-100)
bull_strength = 0.0
bull_strength := bull_strength + (ema_alignment_bull ? 25 : 0)
bull_strength := bull_strength + (rsi_bullish_zone or rsi_oversold_recovery ? 20 : 0)
bull_strength := bull_strength + (macd_bullish_cross ? 25 : macd_momentum_bull ? 15 : 0)
bull_strength := bull_strength + (volume_confirmation_bull or volume_accumulation ? 15 : 0)
bull_strength := bull_strength + (structure_bullish ? 15 : 0)

// Bearish Signal Strength (0-100)
bear_strength = 0.0
bear_strength := bear_strength + (ema_alignment_bear ? 25 : 0)
bear_strength := bear_strength + (rsi_bearish_zone or rsi_overbought_decline ? 20 : 0)
bear_strength := bear_strength + (macd_bearish_cross ? 25 : macd_momentum_bear ? 15 : 0)
bear_strength := bear_strength + (volume_confirmation_bear or volume_distribution ? 15 : 0)
bear_strength := bear_strength + (structure_bearish ? 15 : 0)

// AI Decision Logic
ai_long_signal = bull_strength >= confidence_threshold and bull_strength > bear_strength + 20 and confidence_score >= confidence_threshold
ai_short_signal = bear_strength >= confidence_threshold and bear_strength > bull_strength + 20 and confidence_score >= confidence_threshold

// Wait Conditions (AI Intelligence)
wait_trend_clarity = is_ranging and not bb_squeeze  // Wait for range breakout
wait_volatility_calm = is_volatile and volatility_ratio > 1.5  // Wait for volatility to calm
wait_rsi_reset = (rsi > 75 or rsi < 25) and math.abs(rsi_momentum) < 1  // Wait for RSI reset
wait_volume_confirmation = volume_ratio < 0.8 and is_trending  // Wait for volume pickup
wait_timeframe_alignment = adaptive_timeframe and confidence_score < 40  // Wait for TF alignment
wait_consolidation_break = is_consolidating and math.abs(close - bb_basis) / bb_basis < 0.005  // Wait for consolidation break

// Comprehensive Wait Logic
ai_wait_signal = wait_trend_clarity or wait_volatility_calm or wait_rsi_reset or wait_volume_confirmation or wait_timeframe_alignment or wait_consolidation_break

// Final AI Decision
ai_signal = ai_long_signal ? 1 : ai_short_signal ? -1 : 0
ai_confidence = ai_long_signal ? bull_strength : ai_short_signal ? bear_strength : 0

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 AI SIGNAL CLASSIFICATION & REASONING
// ═══════════════════════════════════════════════════════════════════════════════════════

var string ai_signal_type = "🤖 ANALYZING"
var string ai_reasoning = "AI is analyzing market conditions..."
var string ai_action = "⏳ Monitoring"
var color signal_color = color.new(color.blue, 20)
var string market_regime = "📊 Detecting"
var string confidence_level = "🎯 Calculating"

// AI Signal Classification
if ai_signal == 1
    ai_signal_type := "🚀 LONG ENTRY"
    ai_reasoning := "AI detected strong bullish confluence: " + (ema_alignment_bull ? "✓ EMA Alignment " : "") + (rsi_bullish_zone ? "✓ RSI Momentum " : "") + (macd_bullish_cross ? "✓ MACD Cross " : macd_momentum_bull ? "✓ MACD Momentum " : "") + (volume_confirmation_bull ? "✓ Volume Surge " : "") + (structure_bullish ? "✓ Market Structure" : "")
    ai_action := "📈 ENTER LONG"
    signal_color := color.new(color.lime, 10)

else if ai_signal == -1
    ai_signal_type := "📉 SHORT ENTRY"
    ai_reasoning := "AI detected strong bearish confluence: " + (ema_alignment_bear ? "✓ EMA Alignment " : "") + (rsi_bearish_zone ? "✓ RSI Momentum " : "") + (macd_bearish_cross ? "✓ MACD Cross " : macd_momentum_bear ? "✓ MACD Momentum " : "") + (volume_confirmation_bear ? "✓ Volume Surge " : "") + (structure_bearish ? "✓ Market Structure" : "")
    ai_action := "📉 ENTER SHORT"
    signal_color := color.new(color.red, 10)

else if ai_wait_signal
    ai_signal_type := "⏳ WAIT MODE"
    ai_reasoning := "AI recommends waiting: " + (wait_trend_clarity ? "📊 Range-bound market " : "") + (wait_volatility_calm ? "⚡ High volatility " : "") + (wait_rsi_reset ? "🎯 RSI extreme levels " : "") + (wait_volume_confirmation ? "📊 Low volume " : "") + (wait_timeframe_alignment ? "⏰ Timeframe misalignment " : "") + (wait_consolidation_break ? "📈 Consolidation phase" : "")
    ai_action := "⏳ PATIENCE"
    signal_color := color.new(color.orange, 20)

else
    ai_signal_type := "🔍 SCANNING"
    ai_reasoning := "AI is scanning for optimal entry conditions. Current analysis: " + "Bull Strength: " + str.tostring(math.round(bull_strength)) + "% | " + "Bear Strength: " + str.tostring(math.round(bear_strength)) + "%"
    ai_action := "🔍 MONITORING"
    signal_color := color.new(color.gray, 30)

// Market Regime Classification
market_regime := is_trending ? (trend_direction > 0 ? "📈 BULLISH TREND" : "📉 BEARISH TREND") :
                is_ranging ? "📊 RANGING MARKET" :
                is_volatile ? "⚡ HIGH VOLATILITY" :
                is_consolidating ? "🔄 CONSOLIDATION" : "🤖 ANALYZING"

// Confidence Level
confidence_level := ai_confidence >= 80 ? "🔥 VERY HIGH (" + str.tostring(math.round(ai_confidence)) + "%)" :
                   ai_confidence >= 60 ? "✅ HIGH (" + str.tostring(math.round(ai_confidence)) + "%)" :
                   ai_confidence >= 40 ? "⚠️ MEDIUM (" + str.tostring(math.round(ai_confidence)) + "%)" :
                   ai_confidence >= 20 ? "🔍 LOW (" + str.tostring(math.round(ai_confidence)) + "%)" :
                   "❌ VERY LOW (" + str.tostring(math.round(ai_confidence)) + "%)"

// ═══════════════════════════════════════════════════════════════════════════════════════
// 💰 AI DYNAMIC RISK MANAGEMENT & TRADING EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// AI Entry Conditions
ai_long_entry = ai_signal == 1 and strategy.position_size == 0
ai_short_entry = ai_signal == -1 and strategy.position_size == 0

// Dynamic Risk Adjustment Based on AI Confidence & Market Conditions
volatility_multiplier = volatility_adjustment ? (0.7 + volatility_ratio * 0.6) : 1.0
confidence_multiplier = dynamic_sizing ? (0.5 + (ai_confidence / 100) * 0.8) : 1.0

// Adaptive Take Profit & Stop Loss Levels
dynamic_tp1 = base_tp1 * volatility_multiplier * confidence_multiplier
dynamic_tp2 = base_tp2 * volatility_multiplier * confidence_multiplier
dynamic_sl = base_sl * volatility_multiplier

// Position Size Calculation (AI-driven)
base_position_size = 2.0  // Base 2% risk
ai_position_size = dynamic_sizing ? base_position_size * confidence_multiplier : base_position_size

// Trade Level Variables
var float ai_entry_price = na
var float ai_tp1_price = na
var float ai_tp2_price = na
var float ai_sl_price = na
var float ai_risk_reward = na
var string trade_setup = na

// Long Entry Execution
if ai_long_entry
    ai_entry_price := close
    ai_tp1_price := ai_entry_price * (1 + dynamic_tp1 / 100)
    ai_tp2_price := ai_entry_price * (1 + dynamic_tp2 / 100)
    ai_sl_price := ai_entry_price * (1 - dynamic_sl / 100)
    ai_risk_reward := dynamic_tp2 / dynamic_sl
    trade_setup := "🚀 AI LONG: " + str.tostring(math.round(ai_confidence)) + "% confidence"
    strategy.entry("AI_Long", strategy.long, qty=ai_position_size)

// Short Entry Execution
if ai_short_entry
    ai_entry_price := close
    ai_tp1_price := ai_entry_price * (1 - dynamic_tp1 / 100)
    ai_tp2_price := ai_entry_price * (1 - dynamic_tp2 / 100)
    ai_sl_price := ai_entry_price * (1 + dynamic_sl / 100)
    ai_risk_reward := dynamic_tp2 / dynamic_sl
    trade_setup := "📉 AI SHORT: " + str.tostring(math.round(ai_confidence)) + "% confidence"
    strategy.entry("AI_Short", strategy.short, qty=ai_position_size)

// Advanced Exit Management
if strategy.position_size > 0  // Long position management
    strategy.exit("AI_TP1_Long", "AI_Long", limit=ai_tp1_price, stop=ai_sl_price, qty_percent=50,
                  comment="🎯 TP1 Hit")
    strategy.exit("AI_TP2_Long", "AI_Long", limit=ai_tp2_price, stop=ai_sl_price, qty_percent=50,
                  comment="🚀 TP2 Hit")

if strategy.position_size < 0  // Short position management
    strategy.exit("AI_TP1_Short", "AI_Short", limit=ai_tp1_price, stop=ai_sl_price, qty_percent=50,
                  comment="🎯 TP1 Hit")
    strategy.exit("AI_TP2_Short", "AI_Short", limit=ai_tp2_price, stop=ai_sl_price, qty_percent=50,
                  comment="🚀 TP2 Hit")

// Emergency Exit Conditions (AI Safety)
emergency_exit_long = strategy.position_size > 0 and (rsi > 85 or bear_strength > 80)
emergency_exit_short = strategy.position_size < 0 and (rsi < 15 or bull_strength > 80)

if emergency_exit_long
    strategy.close("AI_Long", comment="🚨 Emergency Exit")
if emergency_exit_short
    strategy.close("AI_Short", comment="🚨 Emergency Exit")

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 BEAUTIFUL VISUAL ELEMENTS & CHART AESTHETICS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Enhanced EMA Plots with Gradient Effects
ema_fast_color = ema_fast_line > ema_slow_line ? color.new(color.aqua, 0) : color.new(color.orange, 0)
ema_slow_color = ema_slow_line > ema_trend_line ? color.new(color.blue, 20) : color.new(color.purple, 20)
ema_trend_color = trend_direction > 0 ? color.new(color.green, 30) : color.new(color.red, 30)

plot(ema_fast_line, "⚡ Fast EMA", color=ema_fast_color, linewidth=2, style=plot.style_line)
plot(ema_slow_line, "🐌 Slow EMA", color=ema_slow_color, linewidth=2, style=plot.style_line)
plot(ema_trend_line, "🌊 Trend EMA", color=ema_trend_color, linewidth=3, style=plot.style_line)

// Dynamic Bollinger Bands with Squeeze Detection
bb_color = bb_squeeze ? color.new(color.yellow, 60) : color.new(color.gray, 80)
bb_upper_plot = plot(bb_upper, "📈 BB Upper", color=bb_color, linewidth=1)
bb_lower_plot = plot(bb_lower, "📉 BB Lower", color=bb_color, linewidth=1)
bb_basis_plot = plot(bb_basis, "📊 BB Basis", color=color.new(color.white, 70), linewidth=1, style=plot.style_circles)
fill(bb_upper_plot, bb_lower_plot, color=bb_squeeze ? color.new(color.yellow, 95) : color.new(color.gray, 97), title="BB Zone")

// AI Signal Visualization
if show_signals
    plotshape(ai_long_entry, "🚀 AI Long", shape.triangleup, location.belowbar,
              color=color.new(color.lime, 0), size=size.large, text="🚀 AI")
    plotshape(ai_short_entry, "📉 AI Short", shape.triangledown, location.abovebar,
              color=color.new(color.red, 0), size=size.large, text="📉 AI")

// Trade Level Visualization
if show_levels and strategy.position_size != 0
    // Entry Level
    plot(ai_entry_price, "🎯 Entry", color=color.new(color.white, 0), linewidth=3, style=plot.style_linebr)

    // Take Profit Levels with Gradient
    plot(ai_tp1_price, "🎯 TP1", color=color.new(color.lime, 20), linewidth=2, style=plot.style_linebr)
    plot(ai_tp2_price, "🚀 TP2", color=color.new(color.green, 0), linewidth=2, style=plot.style_linebr)

    // Stop Loss with Warning Color
    plot(ai_sl_price, "🛡️ Stop Loss", color=color.new(color.red, 0), linewidth=2, style=plot.style_linebr)

// Market Regime Background
regime_color = is_trending ? (trend_direction > 0 ? color.new(color.green, 98) : color.new(color.red, 98)) :
               is_ranging ? color.new(color.orange, 99) :
               is_volatile ? color.new(color.purple, 99) :
               is_consolidating ? color.new(color.blue, 99) : na

bgcolor(regime_color, title="Market Regime")

// Confidence Heatmap
confidence_color = ai_confidence >= 80 ? color.new(color.lime, 90) :
                  ai_confidence >= 60 ? color.new(color.green, 95) :
                  ai_confidence >= 40 ? color.new(color.yellow, 97) :
                  ai_confidence >= 20 ? color.new(color.orange, 98) : color.new(color.red, 99)

bgcolor(confidence_color, title="AI Confidence", editable=false)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🖥️ AI PROFESSIONAL DASHBOARD
// ═══════════════════════════════════════════════════════════════════════════════════════

if show_ai_panel
    // Dynamic Panel Styling
    panel_bg_color = panel_style == "Modern" ? color.new(color.black, 10) :
                    panel_style == "Classic" ? color.new(color.navy, 20) : color.new(color.gray, 30)

    header_color = panel_style == "Modern" ? color.new(color.blue, 30) :
                  panel_style == "Classic" ? color.new(color.purple, 40) : color.new(color.gray, 50)

    var table ai_dashboard = table.new(
         position = position.top_right,
         columns = 2,
         rows = 16,
         bgcolor = panel_bg_color,
         border_width = panel_style == "Minimal" ? 1 : 2,
         border_color = color.new(color.white, 30),
         frame_width = 1,
         frame_color = color.new(color.blue, 50))

    // 🤖 AI Header
    table.cell(ai_dashboard, 0, 0, "🤖 AI TRADING ASSISTANT",
               text_color=color.white, text_size=size.normal,
               bgcolor=header_color, text_font_family=font.family_monospace)
    table.cell(ai_dashboard, 1, 0, "v2.0 PROFESSIONAL",
               text_color=color.new(color.lime, 0), text_size=size.small,
               bgcolor=header_color, text_font_family=font.family_monospace)

    // 🎯 Current AI Decision
    table.cell(ai_dashboard, 0, 1, "🎯 AI DECISION:", text_color=color.white, text_size=size.small)
    table.cell(ai_dashboard, 1, 1, ai_signal_type, text_color=signal_color, text_size=size.small,
               bgcolor=color.new(signal_color, 85))

    // 🧠 AI Reasoning
    table.cell(ai_dashboard, 0, 2, "🧠 REASONING:", text_color=color.white, text_size=size.small)
    table.cell(ai_dashboard, 1, 2, ai_reasoning, text_color=color.new(color.yellow, 0), text_size=size.tiny)

    // 📊 Market Regime
    table.cell(ai_dashboard, 0, 3, "📊 MARKET REGIME:", text_color=color.white, text_size=size.small)
    table.cell(ai_dashboard, 1, 3, market_regime,
               text_color=is_trending ? color.lime : is_ranging ? color.orange : color.yellow, text_size=size.small)

    // 🎯 Confidence Level
    table.cell(ai_dashboard, 0, 4, "🎯 AI CONFIDENCE:", text_color=color.white, text_size=size.small)
    table.cell(ai_dashboard, 1, 4, confidence_level,
               text_color=ai_confidence >= 70 ? color.lime : ai_confidence >= 40 ? color.yellow : color.red,
               text_size=size.small)

    // 📈 Technical Analysis
    table.cell(ai_dashboard, 0, 5, "📈 EMA ALIGNMENT:", text_color=color.white, text_size=size.small)
    ema_status = ema_alignment_bull ? "🟢 BULLISH" : ema_alignment_bear ? "🔴 BEARISH" : "🟡 NEUTRAL"
    table.cell(ai_dashboard, 1, 5, ema_status, text_color=ema_alignment_bull ? color.lime : ema_alignment_bear ? color.red : color.yellow, text_size=size.small)

    // 🎯 RSI Analysis
    table.cell(ai_dashboard, 0, 6, "🎯 RSI MOMENTUM:", text_color=color.white, text_size=size.small)
    rsi_status = str.tostring(math.round(rsi, 1)) + " " + (rsi_bullish_zone ? "🟢" : rsi_bearish_zone ? "🔴" : rsi > 75 ? "🔥" : rsi < 25 ? "❄️" : "🟡")
    table.cell(ai_dashboard, 1, 6, rsi_status, text_color=rsi_bullish_zone ? color.lime : rsi_bearish_zone ? color.red : color.yellow, text_size=size.small)

    // 📊 MACD Signal
    table.cell(ai_dashboard, 0, 7, "📊 MACD SIGNAL:", text_color=color.white, text_size=size.small)
    macd_status = macd_bullish_cross ? "🚀 BULL CROSS" : macd_bearish_cross ? "📉 BEAR CROSS" : macd_momentum_bull ? "🟢 BULL MOMENTUM" : macd_momentum_bear ? "🔴 BEAR MOMENTUM" : "🟡 NEUTRAL"
    table.cell(ai_dashboard, 1, 7, macd_status, text_color=macd_bullish_cross or macd_momentum_bull ? color.lime : macd_bearish_cross or macd_momentum_bear ? color.red : color.yellow, text_size=size.small)

    // 📈 Volume Analysis
    table.cell(ai_dashboard, 0, 8, "📈 VOLUME:", text_color=color.white, text_size=size.small)
    volume_status = volume_ratio > 1.5 ? "🔥 SURGE" : volume_ratio > 1.2 ? "🟢 HIGH" :
                   volume_ratio > 0.8 ? "🟡 NORMAL" : "🔴 LOW"
    table.cell(ai_dashboard, 1, 8, volume_status + " (" + str.tostring(math.round(volume_ratio, 1)) + "x)",
               text_color=volume_ratio > 1.2 ? color.lime : volume_ratio > 0.8 ? color.yellow : color.red,
               text_size=size.small)

    // 📊 Volatility Status
    table.cell(ai_dashboard, 0, 9, "📊 VOLATILITY:", text_color=color.white, text_size=size.small)
    vol_status = volatility_ratio > 1.5 ? "🔥 EXTREME" : volatility_ratio > 1.2 ? "⚡ HIGH" :
                volatility_ratio > 0.8 ? "🟡 NORMAL" : "😴 LOW"
    table.cell(ai_dashboard, 1, 9, vol_status,
               text_color=volatility_ratio > 1.3 ? color.red : volatility_ratio > 1.0 ? color.orange : color.lime,
               text_size=size.small)

    // 💰 Position Information
    if strategy.position_size != 0
        table.cell(ai_dashboard, 0, 10, "💰 POSITION:", text_color=color.white, text_size=size.small)
        position_info = strategy.position_size > 0 ? "🚀 LONG ACTIVE" : "📉 SHORT ACTIVE"
        table.cell(ai_dashboard, 1, 10, position_info, text_color=strategy.position_size > 0 ? color.lime : color.red, text_size=size.small)

        table.cell(ai_dashboard, 0, 11, "🎯 ENTRY PRICE:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 11, str.tostring(ai_entry_price, "#.####"), text_color=color.white, text_size=size.small)

        table.cell(ai_dashboard, 0, 12, "🎯 TP1 (" + str.tostring(dynamic_tp1, "#.#") + "%):", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 12, str.tostring(ai_tp1_price, "#.####"), text_color=color.lime, text_size=size.small)

        table.cell(ai_dashboard, 0, 13, "🚀 TP2 (" + str.tostring(dynamic_tp2, "#.#") + "%):", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 13, str.tostring(ai_tp2_price, "#.####"), text_color=color.green, text_size=size.small)

        table.cell(ai_dashboard, 0, 14, "🛡️ STOP LOSS (" + str.tostring(dynamic_sl, "#.#") + "%):", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 14, str.tostring(ai_sl_price, "#.####"), text_color=color.red, text_size=size.small)

        table.cell(ai_dashboard, 0, 15, "⚖️ RISK/REWARD:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 15, "1:" + str.tostring(ai_risk_reward, "#.#"), text_color=color.yellow, text_size=size.small)
    else
        // No Position Information
        table.cell(ai_dashboard, 0, 10, "💰 POSITION:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 10, "⏳ WAITING", text_color=color.gray, text_size=size.small)

        table.cell(ai_dashboard, 0, 11, "🎯 NEXT ACTION:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 11, ai_action, text_color=color.orange, text_size=size.small)

        table.cell(ai_dashboard, 0, 12, "📊 BULL STRENGTH:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 12, str.tostring(math.round(bull_strength)) + "%", text_color=bull_strength > 60 ? color.lime : bull_strength > 30 ? color.yellow : color.red, text_size=size.small)

        table.cell(ai_dashboard, 0, 13, "📊 BEAR STRENGTH:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 13, str.tostring(math.round(bear_strength)) + "%", text_color=bear_strength > 60 ? color.red : bear_strength > 30 ? color.orange : color.lime, text_size=size.small)

        table.cell(ai_dashboard, 0, 14, "⏰ TIMEFRAME:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 14, timeframe.period, text_color=color.white, text_size=size.small)

        table.cell(ai_dashboard, 0, 15, "🎯 AI SENSITIVITY:", text_color=color.white, text_size=size.small)
        table.cell(ai_dashboard, 1, 15, str.tostring(math.round(ai_sensitivity * 100)) + "%", text_color=color.yellow, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🔔 INTELLIGENT ALERT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════

// AI Entry Alerts with Complete Trade Setup
alertcondition(ai_long_entry, title="🚀 AI Long Entry Signal", message="🤖 AI LONG ENTRY DETECTED! 📊 Confidence: " + str.tostring(math.round(ai_confidence)) + "% 💰 Entry: " + str.tostring(close, "#.####") + " 🎯 TP1: " + str.tostring(ai_tp1_price, "#.####") + " 🚀 TP2: " + str.tostring(ai_tp2_price, "#.####") + " 🛡️ SL: " + str.tostring(ai_sl_price, "#.####") + " ⚖️ R/R: 1:" + str.tostring(ai_risk_reward, "#.#") + " 📈 Regime: " + market_regime)

alertcondition(ai_short_entry, title="📉 AI Short Entry Signal", message="🤖 AI SHORT ENTRY DETECTED! 📊 Confidence: " + str.tostring(math.round(ai_confidence)) + "% 💰 Entry: " + str.tostring(close, "#.####") + " 🎯 TP1: " + str.tostring(ai_tp1_price, "#.####") + " 🚀 TP2: " + str.tostring(ai_tp2_price, "#.####") + " 🛡️ SL: " + str.tostring(ai_sl_price, "#.####") + " ⚖️ R/R: 1:" + str.tostring(ai_risk_reward, "#.#") + " 📈 Regime: " + market_regime)

// AI Wait Condition Alerts
alertcondition(ai_wait_signal, title="⏳ AI Wait Signal", message="🤖 AI RECOMMENDS WAITING 📊 Reason: " + ai_reasoning + " 📈 Market Regime: " + market_regime + " 🎯 Bull Strength: " + str.tostring(math.round(bull_strength)) + "% 📉 Bear Strength: " + str.tostring(math.round(bear_strength)) + "% ⚡ Volatility: " + str.tostring(volatility_ratio, "#.#") + "x")

// High Confidence Alert
alertcondition(ai_confidence >= 85, title="🔥 High Confidence Signal", message="🔥 AI HIGH CONFIDENCE DETECTED! 📊 Confidence: " + str.tostring(math.round(ai_confidence)) + "% 🎯 Signal: " + ai_signal_type + " 📈 Market: " + market_regime)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🎨 FINAL VISUAL ENHANCEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Signal Strength Visualization
plotchar(ai_confidence >= 80, "🔥", "🔥", location.top, color.new(color.orange, 0), size=size.small)
plotchar(ai_signal == 1, "🚀", "🚀", location.bottom, color.new(color.lime, 0), size=size.normal)
plotchar(ai_signal == -1, "📉", "📉", location.top, color.new(color.red, 0), size=size.normal)

// ═══════════════════════════════════════════════════════════════════════════════════════
// 🤖 AI TRADING SYSTEM SUMMARY
// ═══════════════════════════════════════════════════════════════════════════════════════
//
// 🎯 CORE FEATURES:
// • Advanced AI decision-making with confidence scoring
// • Multi-timeframe market regime detection
// • Dynamic risk management based on volatility & confidence
// • Professional visual interface with real-time AI insights
// • Intelligent wait conditions for optimal entry timing
//
// 📊 TECHNICAL ANALYSIS:
// • EMA alignment analysis (9, 21, 55 periods)
// • Advanced RSI momentum detection with extremes filtering
// • MACD cross and momentum confirmation
// • Volume surge and trend analysis
// • Bollinger Bands squeeze detection
// • Market structure analysis (higher highs/lower lows)
//
// 🧠 AI INTELLIGENCE:
// • Confidence-based position sizing
// • Volatility-adjusted take profits and stop losses
// • Market regime adaptive strategies
// • Multi-factor signal strength calculation
// • Comprehensive wait condition logic
//
// 💰 RISK MANAGEMENT:
// • Dynamic TP1/TP2 levels based on market conditions
// • Adaptive stop losses with volatility adjustment
// • Emergency exit conditions for extreme scenarios
// • Professional 1:2-3 risk/reward ratios
//
// 🎨 USER EXPERIENCE:
// • Beautiful, professional dashboard with emojis
// • Real-time AI reasoning and explanations
// • Color-coded confidence and regime indicators
// • Comprehensive alert system with trade details
// • Multiple visual styles (Modern/Classic/Minimal)
//
// ═══════════════════════════════════════════════════════════════════════════════════════